import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Chrome, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { Link, useNavigate } from "react-router-dom";
import { validateSignUpForm, FormErrors } from "@/lib/auth";

export const SimpleSignUpForm = () => {
  const { signUp, signInWithProvider, loading } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const formErrors = validateSignUpForm(email, password, confirmPassword);
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const { error } = await signUp(email, password);

      if (!error) {
        // Account created successfully, user should be automatically signed in
        // Navigate to home page
        navigate("/");
      } else {
        setErrors({ general: error.message });
      }
    } catch (err: any) {
      console.error("Sign up error:", err);
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignUp = async () => {
    try {
      const { error } = await signInWithProvider('google');
      if (error) {
        toast.error(error.message);
      }
    } catch (err: any) {
      console.error("Google sign up error:", err);
      toast.error("Failed to sign up with Google");
    }
  };



  return (
    <div className="w-full max-w-sm mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="space-y-6"
      >
        <div>
          <h1 className="text-2xl font-semibold text-white mb-8">Create your account</h1>
        </div>

        {/* Social Sign Up Button */}
        <div className="space-y-3">
          <Button
            onClick={handleGoogleSignUp}
            variant="outline"
            className="w-full bg-gray-800 border-gray-700 text-white hover:bg-gray-700 transition-colors"
            disabled={loading}
          >
            <Chrome className="w-4 h-4 mr-2" />
            Sign up with Google
          </Button>
        </div>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-gray-900 px-2 text-gray-400">OR</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-gray-600"
              placeholder="Email"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-gray-300">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-gray-600 pr-10"
                placeholder="Password (min. 6 characters)"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-300"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-400 text-sm">{errors.password}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="text-sm text-gray-300">
              Confirm Password
            </Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-gray-600 pr-10"
                placeholder="Confirm your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-300"
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-red-400 text-sm">{errors.confirmPassword}</p>
            )}
          </div>

          {/* Terms and Privacy */}
          <div className="flex items-start space-x-3 p-4 rounded-lg bg-gray-800/50 border border-gray-700/50">
            <div className="relative">
              <input
                type="checkbox"
                id="terms"
                className="peer h-4 w-4 rounded border-2 border-gray-600 bg-gray-800 text-pegasus-orange focus:ring-2 focus:ring-pegasus-orange/50 focus:border-pegasus-orange transition-all duration-200 cursor-pointer"
                required
              />
              <div className="absolute inset-0 rounded border-2 border-transparent bg-gradient-to-r from-pegasus-orange/20 to-pegasus-blue/20 opacity-0 peer-hover:opacity-100 transition-opacity duration-200 pointer-events-none"></div>
            </div>
            <label htmlFor="terms" className="text-sm text-gray-300 leading-relaxed cursor-pointer">
              I agree to our and read this{" "}
              <Link
                to="/terms-of-service"
                className="text-pegasus-orange hover:text-pegasus-orange/80 font-medium underline decoration-pegasus-orange/50 hover:decoration-pegasus-orange transition-all duration-200"
              >
                Terms of Service
              </Link>
            </label>
          </div>

          {errors.general && (
            <p className="text-red-400 text-sm text-center">{errors.general}</p>
          )}

          <Button
            type="submit"
            disabled={isSubmitting || loading || !email || !password || !confirmPassword}
            className="w-full bg-white text-black hover:bg-gray-100 transition-colors"
          >
            {isSubmitting ? "Creating account..." : "Sign up"}
          </Button>
        </form>

        {/* Sign In Link */}
        <div className="text-center">
          <span className="text-gray-400 text-sm">
            Already have an account?{" "}
            <Link
              to="/sign-in"
              className="text-white hover:underline font-medium"
            >
              Sign in
            </Link>
          </span>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleSignUpForm;
