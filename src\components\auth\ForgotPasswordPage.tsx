import { ForgotPasswordForm } from "./ForgotPasswordForm";
import { AnimatedFeatureDisplay } from "./AnimatedFeatureDisplay";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { Home } from "lucide-react";
import { Button } from "@/components/ui/button";

export const ForgotPasswordPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex">
      {/* Left side - Animated Features */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-orange-900/20" />
        <AnimatedFeatureDisplay />
      </div>

      {/* Right side - Reset Password Form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center px-8 lg:px-16 relative">
        {/* Home Button */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="absolute top-8 left-8"
        >
          <Link to="/">
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-white hover:bg-gray-800/50 transition-all duration-300"
            >
              <Home className="w-4 h-4 mr-2" />
              Home
            </Button>
          </Link>
        </motion.div>

        {/* Form Container */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="w-full max-w-md mx-auto"
        >
          <ForgotPasswordForm />
        </motion.div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="absolute bottom-8 left-8 right-8 text-center"
        >
          <p className="text-gray-500 text-sm">
            © 2024 Pegasus Tools. All rights reserved.
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
