import { SimpleSignInForm } from "./SimpleSignInForm";
import { AnimatedFeatureDisplay } from "./AnimatedFeatureDisplay";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { Home } from "lucide-react";
import { Button } from "@/components/ui/button";

export const SignInPage = () => {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row relative">
      {/* Back To Home Button */}
      <motion.div
        className="absolute top-6 left-6 z-50"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Link to="/">
          <Button
            variant="ghost"
            size="sm"
            className="bg-gray-800/80 backdrop-blur-sm border border-gray-700/50 text-gray-200 hover:bg-gray-700/90 hover:text-white transition-all duration-300 group"
          >
            <Home className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
            Back To Home
          </Button>
        </Link>
      </motion.div>

      {/* Left Section - Authentication Form */}
      <div className="w-full lg:w-1/2 bg-gray-900 flex items-center justify-center p-8">
        <SimpleSignInForm />
      </div>

      {/* Right Section - Elegant Animated Background with Feature Display */}
      <div className="w-full lg:w-1/2 relative overflow-hidden min-h-[50vh] lg:min-h-screen">
        {/* Base Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-orange-900" />

        {/* Subtle Animated Gradient Overlay */}
        <motion.div
          className="absolute inset-0 opacity-60"
          animate={{
            background: [
              "linear-gradient(45deg, rgba(59, 130, 246, 0.4) 0%, rgba(124, 58, 237, 0.3) 50%, rgba(249, 115, 22, 0.4) 100%)",
              "linear-gradient(45deg, rgba(249, 115, 22, 0.4) 0%, rgba(59, 130, 246, 0.3) 50%, rgba(124, 58, 237, 0.4) 100%)",
              "linear-gradient(45deg, rgba(124, 58, 237, 0.4) 0%, rgba(249, 115, 22, 0.3) 50%, rgba(59, 130, 246, 0.4) 100%)",
            ]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating Orbs - Gentle Movement */}
        <div className="absolute inset-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={`orb-${i}`}
              className="absolute rounded-full bg-white/5 backdrop-blur-sm"
              style={{
                width: `${120 + i * 40}px`,
                height: `${120 + i * 40}px`,
                left: `${20 + i * 20}%`,
                top: `${30 + i * 10}%`,
              }}
              animate={{
                x: [0, 20, -10, 0],
                y: [0, -15, 10, 0],
                scale: [1, 1.05, 0.95, 1],
                opacity: [0.3, 0.5, 0.3],
              }}
              transition={{
                duration: 25 + i * 5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: i * 3,
              }}
            />
          ))}
        </div>

        {/* Delicate Particle System */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={`particle-${i}`}
              className="absolute w-1 h-1 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -50, 0],
                opacity: [0, 0.6, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 12 + Math.random() * 8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 10,
              }}
            />
          ))}
        </div>

        {/* Geometric Shapes - Slow Rotation */}
        <div className="absolute inset-0">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={`shape-${i}`}
              className="absolute border border-white/10"
              style={{
                width: `${80 + i * 30}px`,
                height: `${80 + i * 30}px`,
                left: `${60 + i * 10}%`,
                top: `${20 + i * 25}%`,
                borderRadius: i % 2 === 0 ? '50%' : '20%',
              }}
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 40 + i * 10,
                repeat: Infinity,
                ease: "linear",
                delay: i * 5,
              }}
            />
          ))}
        </div>

        {/* Subtle Wave Effect */}
        <motion.div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `repeating-linear-gradient(
              45deg,
              transparent,
              transparent 10px,
              rgba(255,255,255,0.1) 10px,
              rgba(255,255,255,0.1) 11px
            )`,
          }}
          animate={{
            backgroundPosition: ['0px 0px', '20px 20px'],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Soft Overlay */}
        <div className="absolute inset-0 bg-black/30" />

        {/* Animated Feature Display */}
        <div className="relative z-10 h-full">
          <AnimatedFeatureDisplay />
        </div>
      </div>
    </div>
  );
};

export default SignInPage;
