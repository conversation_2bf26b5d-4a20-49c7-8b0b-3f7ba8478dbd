import { SimpleSignUpForm } from "./SimpleSignUpForm";
import { AnimatedFeatureDisplay } from "./AnimatedFeatureDisplay";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { Home } from "lucide-react";
import { Button } from "@/components/ui/button";

export const SignUpPage = () => {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row relative">
      {/* Back To Home Button */}
      <motion.div
        className="absolute top-6 left-6 z-50"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Link to="/">
          <Button
            variant="ghost"
            size="sm"
            className="bg-gray-800/80 backdrop-blur-sm border border-gray-700/50 text-gray-200 hover:bg-gray-700/90 hover:text-white transition-all duration-300 group"
          >
            <Home className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
            Back To Home
          </Button>
        </Link>
      </motion.div>

      {/* Left Section - Authentication Form */}
      <div className="w-full lg:w-1/2 bg-gray-900 flex items-center justify-center p-8">
        <SimpleSignUpForm />
      </div>

      {/* Right Section - Elegant Animated Background with Feature Display */}
      <div className="w-full lg:w-1/2 relative overflow-hidden min-h-[50vh] lg:min-h-screen">
        {/* Base Gradient Background - Orange Theme */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-900 via-purple-900 to-blue-900" />

        {/* Subtle Animated Gradient Overlay */}
        <motion.div
          className="absolute inset-0 opacity-60"
          animate={{
            background: [
              "linear-gradient(45deg, rgba(249, 115, 22, 0.4) 0%, rgba(124, 58, 237, 0.3) 50%, rgba(59, 130, 246, 0.4) 100%)",
              "linear-gradient(45deg, rgba(59, 130, 246, 0.4) 0%, rgba(249, 115, 22, 0.3) 50%, rgba(124, 58, 237, 0.4) 100%)",
              "linear-gradient(45deg, rgba(124, 58, 237, 0.4) 0%, rgba(59, 130, 246, 0.3) 50%, rgba(249, 115, 22, 0.4) 100%)",
            ]
          }}
          transition={{
            duration: 22,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating Orbs - Gentle Movement */}
        <div className="absolute inset-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={`orb-${i}`}
              className="absolute rounded-full bg-white/5 backdrop-blur-sm"
              style={{
                width: `${100 + i * 35}px`,
                height: `${100 + i * 35}px`,
                left: `${25 + i * 18}%`,
                top: `${25 + i * 12}%`,
              }}
              animate={{
                x: [0, -15, 25, 0],
                y: [0, 20, -10, 0],
                scale: [1, 1.08, 0.92, 1],
                opacity: [0.2, 0.4, 0.2],
              }}
              transition={{
                duration: 28 + i * 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: i * 4,
              }}
            />
          ))}
        </div>

        {/* Delicate Particle System */}
        <div className="absolute inset-0">
          {[...Array(18)].map((_, i) => (
            <motion.div
              key={`particle-${i}`}
              className="absolute w-1 h-1 bg-white/25 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -60, 0],
                x: [0, Math.random() * 30 - 15, 0],
                opacity: [0, 0.7, 0],
                scale: [0, 1.2, 0],
              }}
              transition={{
                duration: 15 + Math.random() * 10,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 12,
              }}
            />
          ))}
        </div>

        {/* Geometric Shapes - Slow Rotation */}
        <div className="absolute inset-0">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={`shape-${i}`}
              className="absolute border border-white/8"
              style={{
                width: `${70 + i * 25}px`,
                height: `${70 + i * 25}px`,
                left: `${50 + i * 12}%`,
                top: `${15 + i * 30}%`,
                borderRadius: i % 2 === 0 ? '30%' : '50%',
              }}
              animate={{
                rotate: [0, -360],
                scale: [1, 1.15, 1],
                opacity: [0.08, 0.25, 0.08],
              }}
              transition={{
                duration: 45 + i * 15,
                repeat: Infinity,
                ease: "linear",
                delay: i * 7,
              }}
            />
          ))}
        </div>

        {/* Flowing Lines Effect */}
        <motion.div
          className="absolute inset-0 opacity-4"
          style={{
            backgroundImage: `repeating-linear-gradient(
              -45deg,
              transparent,
              transparent 15px,
              rgba(255,255,255,0.08) 15px,
              rgba(255,255,255,0.08) 16px
            )`,
          }}
          animate={{
            backgroundPosition: ['0px 0px', '30px 30px'],
          }}
          transition={{
            duration: 35,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Soft Overlay */}
        <div className="absolute inset-0 bg-black/30" />

        {/* Animated Feature Display */}
        <div className="relative z-10 h-full">
          <AnimatedFeatureDisplay />
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;
